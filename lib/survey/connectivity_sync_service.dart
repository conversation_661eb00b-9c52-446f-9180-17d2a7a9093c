import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:geocoding/geocoding.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:schnell_pole_installation/survey/survey_service.dart';
import 'package:schnell_pole_installation/survey/s3_upload_service.dart';
import 'package:schnell_pole_installation/survey/models/survey_image_model.dart';
import 'package:schnell_pole_installation/utils/image_compression_util.dart';
import 'package:schnell_pole_installation/utils/utility.dart';

/// Connectivity-based background sync service
/// Automatically syncs data whenever internet connection is restored
/// No timers, no periodic checks - pure event-driven sync
class ConnectivitySyncService {
  static ConnectivitySyncService? _instance;
  static ConnectivitySyncService get instance =>
      _instance ??= ConnectivitySyncService._();

  ConnectivitySyncService._();

  // Sync state management
  bool _isInitialized = false;
  bool _isSyncing = false;
  bool _wasOffline = false;

  // Debounce mechanism for sync operations
  static DateTime? _lastSyncAttempt;
  static const Duration _syncDebounceDelay = Duration(seconds: 3);

  // Connectivity monitoring
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Progress tracking
  final StreamController<SyncProgress> _progressController =
      StreamController<SyncProgress>.broadcast();
  Stream<SyncProgress> get progressStream => _progressController.stream;

  // Sync status tracking
  final StreamController<SyncStatus> _statusController =
      StreamController<SyncStatus>.broadcast();
  Stream<SyncStatus> get statusStream => _statusController.stream;

  /// Initialize connectivity-based sync
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      log('Initializing Connectivity-based background sync...');

      // Start listening to connectivity changes
      _startConnectivityMonitoring();

      _isInitialized = true;
      log('Connectivity-based background sync initialized successfully');
    } catch (e) {
      log('Failed to initialize Connectivity sync: $e');
      rethrow;
    }
  }

  /// Start monitoring connectivity changes
  void _startConnectivityMonitoring() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        _handleConnectivityChange(results);
      },
    );

    log('Started monitoring connectivity changes');
  }

  /// Handle connectivity state changes
  void _handleConnectivityChange(List<ConnectivityResult> results) async {
    final bool isConnected = results.any((result) =>
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.ethernet);

    log('Connectivity changed: $results, Connected: $isConnected');

    if (isConnected) {
      // Internet is available - trigger sync
      log('Internet connection available - triggering background sync');
      _triggerBackgroundSync();
    }

    _wasOffline = !isConnected;
  }

  /// Trigger immediate background sync
  void _triggerBackgroundSync() {
    // Don't block the connectivity callback - run sync asynchronously
    Future.microtask(() => _performBackgroundSync());
  }

  /// Perform manual sync (called by UI)
  Future<void> performManualSync() async {
    if (!_isInitialized) {
      await initialize();
    }

    // Manual sync bypasses debounce (user-initiated)
    _lastSyncAttempt = null;
    await _performBackgroundSync();
  }

  /// Internal method to perform sync
  Future<void> _performBackgroundSync() async {
    // Debounce: prevent rapid successive calls
    final now = DateTime.now();
    if (_lastSyncAttempt != null &&
        now.difference(_lastSyncAttempt!) < _syncDebounceDelay) {
      log('Background sync attempt debounced - too soon since last attempt');
      return;
    }
    _lastSyncAttempt = now;

    if (_isSyncing) {
      log('Sync already in progress, skipping...');
      return;
    }

    try {
      _isSyncing = true;
      _statusController.add(SyncStatus.syncing);

      log('isolate --- Starting connectivity-triggered background sync...');

      // Double-check internet connectivity
      bool isOnline = await Utility.isConnected();
      if (!isOnline) {
        log('isolate --- No internet connection confirmed, skipping sync');
        _statusController.add(SyncStatus.idle);
        return;
      }

      // Sync surveys and images
      await Future.wait([
        _syncSurveyData(),
        _syncImages(),
      ]);

      _statusController.add(SyncStatus.completed);
      log('isolate --- Background sync completed successfully');
    } catch (e) {
      log('isolate --- Background sync failed: $e');
      _statusController.add(SyncStatus.error);
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync survey data
  Future<void> _syncSurveyData() async {
    try {
      log('isolate --- Starting survey data sync...');

      final box = await Hive.openBox('surveyBox');
      final totalRecords = box.keys.length;

      if (totalRecords == 0) {
        log('isolate --- No survey records to sync');
        return;
      }

      log('isolate --- Found $totalRecords survey records to sync');

      int syncedCount = 0;
      int errorCount = 0;

      // Process surveys in small batches to avoid blocking UI
      const batchSize = 20;
      final keys = box.keys.toList();

      for (int i = 0; i < keys.length; i += batchSize) {
        final batchKeys = keys.skip(i).take(batchSize);

        for (final key in batchKeys) {
          try {
            final surveyData = box.get(key);
            if (surveyData != null && surveyData['isUploaded'] != true) {
              log('isolate --- Asset Type in background sync: ${surveyData['assetType']}');
              log('isolate --- ${jsonEncode(surveyData)} is ready to upload');

              final response = await _syncSurvey(surveyData);
              if (response) {
                // Mark as uploaded
                surveyData['isUploaded'] = true;
                await box.put(key, surveyData);
                syncedCount++;
                log('isolate --- Survey $key uploaded successfully');
              } else {
                errorCount++;
                log('isolate --- Failed to upload survey $key: $response');
              }
            }
          } catch (e) {
            errorCount++;
            log('isolate --- Error syncing survey $key: $e');
          }
        }

        // Progress update
        _progressController.add(SyncProgress(
          current: i + batchSize,
          total: keys.length,
          operation: 'Syncing surveys',
        ));

        // Small delay between batches to prevent UI blocking
        await Future.delayed(const Duration(milliseconds: 50));
      }

      log('isolate --- Survey sync completed: $syncedCount/$totalRecords synced, $errorCount errors');
    } catch (e) {
      log('isolate --- Survey sync failed: $e');
    }
  }

  Future<String> getLocation(lat, long) async {
    List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
    Placemark place = placemarks[0];
    var locationData =
        '${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
    // location = '${place.name},${place.street},${place.subLocality}, ${place.locality},${place.postalCode},';
    return locationData;
  }

  Future<bool> _syncSurvey(data) async {
    final SurveyService surveyService = SurveyService();

    var locationCheck = data['location'];
    var manualEnteredLocation = data['manualEnteredLocation'];
    String? filteredLandMark;
    if (locationCheck == null) {
      String landMark = await getLocation(data['latitude'], data['longitude']);
      String? concatenatedLocation = '$manualEnteredLocation,$landMark';
      filteredLandMark = concatenatedLocation
          .replaceAll(RegExp(r',\s*'), ',')
          .replaceAll(RegExp(r'^,\s*'), '')
          .trim();
    }

    final isPole = data['assetType'] == 'Pole';
    final isSwitchPoint = data['assetType'] == 'Switch Point';
    final isTransformer = data['assetType'] == 'Transformer';
    final installedOn = int.tryParse(data['installedOn'] ?? '0') ?? 0;
    final armCount = int.tryParse(data['armCount'] ?? '0') ?? 0;

    final Map<String, dynamic> postData = {
      "roadType": data['roadType'],
      "trafficDensity": data['trafficDensity'],
      "trafficSpeed": data['trafficSpeed'],
      "assetType": data['assetType'],
      "customerId": data['customerId'],
      "wardId": data['wardId'],
      if (data['comments'] != '') "remarks": data['comments'],
      "region": data['region'],
      "zoneName": data['zone'],
      "wardName": data['ward'],
      "installedBy": data['installedBy'],
      "installedOn": installedOn,
      "state": 'INSTALLED',
      "latitude": data['latitude'],
      "longitude": data['longitude'],
      "location": filteredLandMark,
      "accuracy": double.parse(data['accuracy'] == '' ? '0' : data['accuracy']),
      "altitude": data['altitude'],
      "existingRoadCategory": data['roadCategory'],
      "roadWidth": data['roadWidth'],
      "vehicleAccess": data['vehicleAccess'],
      "signalStrength": {
        data["carrierName"]: data['signalStrengthLevel'],
      },
      "auditImg": [
        if (data['uuidFileName1'] != '') data['uuidFileName1'],
        if (data['uuidFileName2'] != '') data['uuidFileName2'],
        if (data['uuidFileName3'] != '') data['uuidFileName3'],
      ]
    };

    if (isPole) {
      postData.addAll({
        "name": data['poleNumber'],
        "condition": data['poleCondition'],
      });

      if (data['switchPointNo'] != '') {
        postData["switchPointNo"] = data['switchPointNo'];
      }
      if (data['escomPoleNumber'] != '') {
        postData["ebPoleNo"] = data['escomPoleNumber'];
      }
      if (data['exCorpPoleNo'] != '') {
        postData["exCorpPoleNo"] = data['exCorpPoleNo'];
      }
      if (data['poleTransformerNo'] != '') {
        postData["transformerNo"] = data['poleTransformerNo'];
      }
      if (data['poleCondition'] != 'Missing') {
        postData.addAll({
          "type": data['poleType'],
          "height": data['poleHeight'],
          "earthingRequired": data['earthingRequired'],
          "manualSwitchControl": data['manualSwitchControl'],
          "incomingTransmissionLine": data['incomingTransLine'],
          "incomingTransmissionType": data['incomingTransType'],
          "armCount": armCount,
          "armDetails": {
            "count": armCount,
            "length": data['armLength'],
            "condition": [
              {"condition": "Good", "count": data['goodArmCount']},
              {"condition": "Bad", "count": data['badArmCount']},
              {"condition": "Missing", "count": data['missingArmCount']},
            ]
          },
        });
        if (armCount > 0) {
          if (data['selectedLamps'].length > 0) {
            postData['lampProfiles'] = data['selectedLamps'];
          }
          postData['lightDetails'] = [
            {
              "condition": "Working",
              "count": data['workingCount'],
            },
            {
              "condition": "Not Working",
              "count": data['notWorkingCount'],
            },
            {
              "condition": "Missing",
              "count": data['missingCount'],
            },
          ];
        }

        if (data['poleType'] == 'High Mast(HM)') {
          postData['motorCondition'] = data['motorCondition'];
          if (data['motorCondition'] != 'Missing') {
            postData["motorDetails"] = {
              'rating': data['motorRating'],
              'make': data['motorMake'],
              'model': data['motorModel']
            };
            postData['winchCondition'] = data['winchCondition'];
            postData['ropeCondition'] = data['ropeCondition'];
          }
        }
        if (data['poleType'] != 'High Mast(HM)' &&
            data['poleType'] != 'Mini Mast(MM)') {
          postData["span"] = data['poleSpan'];
          postData["bracketMountingHeight"] = data['bracketMountingHeight'];
          if (data['clampType'] != 'Clamp Type Not Required') {
            postData["clampDimension"] = {
              "length": data['clampTypeLength'],
              "width": data['clampTypeWidth'],
              "unit": data['clampTypeUnits'],
            };
          }
        }
      }
    }

    if (isSwitchPoint) {
      postData.addAll({
        "switchPointNumber": data['spNo'],
        "switchPointType": data['spType'],
        "connectedLoad": (data['spconnectedLoad']?.isNotEmpty ?? false)
            ? '${data['spconnectedLoad']} kW'
            : '0 kW',
        "condition": data['spCondition'],
        "earthingCondition": data['spEarthingCondition'],
      });

      if (data['rrNo'] != '') postData["rrNumber"] = data['rrNo'];
      if (data['spId'] != '') postData["panelId"] = data['spId'];
      if (data['spTransformerNo'] != '') {
        postData["transformerNo"] = data['spTransformerNo'];
      }

      if (data['spMeter'] == 'DP/Manual') {
        postData["meterDetails"] = {
          "type": data['spMeterType'],
        };
      } else {
        postData["meterDetails"] = {
          "no": data['spMeterNo'],
          "type": data['spMeterType'],
          "make": data['spMeterMake'],
          "phase": data['spMeterPhase'],
          "status": data['spMeter'],
        };
      }
    }

    if (isTransformer) {
      postData.addAll({
        "transformerNumber": data['transformerNo'],
        "capacity": (data['transCapacity'].isNotEmpty ?? false)
            ? '${data['transCapacity']} kW'
            : '0 kW',
      });
    }

    var result = await surveyService.updateSurveyDetailService(postData);
    if (result == "200") {
      log('isolate --- $postData survey details uploaded successfully');
      // final prefs = await SharedPreferences.getInstance();
      // bool isLocationTrackingRequi =
      //     prefs.getBool('isLocationTrackingRquired') ?? false;

      // if (isLocationTrackingRequi) {
      //   String activityName = isPole
      //       ? 'Pole Survey'
      //       : isSwitchPoint
      //           ? 'Switch Point Survey'
      //           : isTransformer
      //               ? 'Transformer Survey'
      //               : '';
      //   String entityName = '';
      //   if (isPole) {
      //     entityName = postData['name'];
      //     log(entityName);
      //   } else if (isSwitchPoint) {
      //     entityName = postData['switchPointNumber'];
      //   } else if (isTransformer) {
      //     entityName = postData['transformerNumber'];
      //     log(entityName);
      //   }
      //   _poleDetailController.activityLocationTracking(
      //       activityName, entityName);
      // }
      return true;
    } else if (result == "400") {
      return true;
    }
    return false;
  }

  /// Sync images using the same logic as uploadCapturedImages in base_survey.dart
  Future<void> _syncImages() async {
    try {
      log('Starting image sync...');

      final imagesBox = await Hive.openBox('imagesBox');
      log('Starting image upload process. Total image records: ${imagesBox.length}');

      if (imagesBox.length == 0) {
        log('No images to sync');
        return;
      }

      int syncedCount = 0;
      int errorCount = 0;

      for (int i = 0; i < imagesBox.length; i++) {
        try {
          final imageMap = imagesBox.getAt(i) as Map<dynamic, dynamic>;
          final model = MultiCapturedImageModel.fromMap(imageMap);

          // Skip if all images are already uploaded
          if (model.isAllUploaded) {
            log('isolate --- All images for record $i already uploaded, skipping');
            continue;
          }

          bool updated = false;

          // Upload image 1
          if (!model.isUploaded1) {
            if (model.fileName1.isNotEmpty && model.filePath1.isNotEmpty) {
              log('isolate --- Uploading 1st image: ${model.fileName1}');
              final res1 = await _updateSurveyImageFromFile(
                  model.filePath1, model.fileName1);
              log('isolate --- res1: $res1');
              if (res1 == true) {
                model.isUploaded1 = true;
                updated = true;
                syncedCount++;
                log('isolate ---Image 1 uploaded: ${model.fileName1}');
                // Delete the compressed file after successful upload
                await ImageCompressionUtil.deleteCompressedImage(
                    model.filePath1);
              } else {
                errorCount++;
              }
            } else {
              // Mark as uploaded if no image was captured
              model.isUploaded1 = true;
              updated = true;
              log('isolate --- Image 1 not captured - marking as uploaded');
            }
          }

          // Upload image 2
          if (!model.isUploaded2) {
            if (model.fileName2.isNotEmpty && model.filePath2.isNotEmpty) {
              log('isolate --- Uploading 2nd image: ${model.fileName2}');
              final res2 = await _updateSurveyImageFromFile(
                  model.filePath2, model.fileName2);
              log('isolate --- res2: $res2');
              if (res2 == true) {
                model.isUploaded2 = true;
                updated = true;
                syncedCount++;
                log('isolate --- Image 2 uploaded: ${model.fileName2}');
                // Delete the compressed file after successful upload
                await ImageCompressionUtil.deleteCompressedImage(
                    model.filePath2);
              } else {
                errorCount++;
              }
            } else {
              // Mark as uploaded if no image was captured
              model.isUploaded2 = true;
              updated = true;
              log('isolate --- Image 2 not captured - marking as uploaded');
            }
          }

          // Upload image 3
          if (!model.isUploaded3) {
            if (model.fileName3.isNotEmpty && model.filePath3.isNotEmpty) {
              log('isolate --- Uploading 3rd image: ${model.fileName3}');
              final res3 = await _updateSurveyImageFromFile(
                  model.filePath3, model.fileName3);
              log('isolate --- res3: $res3');
              if (res3 == true) {
                model.isUploaded3 = true;
                updated = true;
                syncedCount++;
                log('isolate --- Image 3 uploaded: ${model.fileName3}');
                // Delete the compressed file after successful upload
                await ImageCompressionUtil.deleteCompressedImage(
                    model.filePath3);
              } else {
                errorCount++;
              }
            } else {
              // Mark as uploaded if no image was captured
              model.isUploaded3 = true;
              updated = true;
              log('isolate --- Image 3 not captured - marking as uploaded');
            }
          }

          // Update the record if any changes were made
          if (updated) {
            await imagesBox.putAt(i, model.toMap());
            log('isolate --- Updated image record $i with new upload status');
          }

          // Log final status
          final capturedCount = [
            model.fileName1,
            model.fileName2,
            model.fileName3
          ].where((filename) => filename.isNotEmpty).length;
          final uploadedCount = [
            model.isUploaded1,
            model.isUploaded2,
            model.isUploaded3
          ].where((uploaded) => uploaded).length;

          if (model.isAllUploaded) {
            log("isolate --- All images at index $i processed. Captured: $capturedCount, Uploaded: $uploadedCount");
          } else {
            log("isolate --- Images at index $i partially processed. Captured: $capturedCount, Uploaded: $uploadedCount. Will retry later.");
          }
        } catch (e) {
          errorCount++;
          log('isolate --- Error processing image record $i: $e');
        }
      }

      log('isolate --- Image sync completed: $syncedCount images uploaded, $errorCount errors');
    } catch (e) {
      log('isolate --- Image sync failed: $e');
    }
  }

  // /// Update survey image using S3 service (same logic as in survey controller)
  // Future<bool> _updateSurveyImage(String deviceImage, String filename) async {
  //   try {
  //     final s3Service = S3UploadService();
  //     final result =
  //         await s3Service.updateSurveyImageServiceS3(deviceImage, filename);

  //     if (result == "200") {
  //       return true;
  //     } else if (result == "400") {
  //       return false;
  //     }
  //     return false;
  //   } catch (e) {
  //     log('Error uploading image $filename: $e');
  //     return false;
  //   }
  // }

  /// Update survey image from file path using S3 service (optimized version)
  Future<bool> _updateSurveyImageFromFile(
      String filePath, String filename) async {
    try {
      final s3Service = S3UploadService();
      final result =
          await s3Service.updateSurveyImageFileS3(filePath, filename);

      if (result == "200") {
        return true;
      } else if (result == "400") {
        return false;
      }
      return false;
    } catch (e) {
      log('Error uploading image file $filename: $e');
      return false;
    }
  }

  /// Stop connectivity monitoring
  void stopMonitoring() {
    _connectivitySubscription?.cancel();
    _connectivitySubscription = null;
    log('Stopped connectivity monitoring');
  }

  /// Dispose resources
  void dispose() {
    stopMonitoring();
    _progressController.close();
    _statusController.close();
    _isInitialized = false;
  }

  /// Reset singleton instance
  static void resetInstance() {
    _instance?.dispose();
    _instance = null;
  }

  /// Get current sync status
  bool get isSyncing => _isSyncing;
  bool get isInitialized => _isInitialized;
}

/// Sync progress model
class SyncProgress {
  final int current;
  final int total;
  final String operation;
  final double percentage;

  SyncProgress({
    required this.current,
    required this.total,
    required this.operation,
  }) : percentage = total > 0 ? (current / total * 100) : 0;

  factory SyncProgress.fromMap(Map<String, dynamic> map) {
    return SyncProgress(
      current: map['current'] ?? 0,
      total: map['total'] ?? 0,
      operation: map['operation'] ?? '',
    );
  }
}

/// Sync status enum
enum SyncStatus {
  idle,
  syncing,
  completed,
  error,
}
