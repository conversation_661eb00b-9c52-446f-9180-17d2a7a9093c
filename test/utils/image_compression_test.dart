import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:schnell_pole_installation/utils/image_compression_util.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

// Mock path provider for testing
class MockPathProviderPlatform extends Fake
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {
  @override
  Future<String?> getApplicationDocumentsPath() async {
    return '/tmp/test_documents';
  }
}

void main() {
  group('ImageCompressionUtil Tests', () {
    late Directory testDir;
    late File testImageFile;

    setUpAll(() async {
      // Register mock path provider
      PathProviderPlatform.instance = MockPathProviderPlatform();
      
      // Create test directory
      testDir = Directory('/tmp/test_documents');
      if (!await testDir.exists()) {
        await testDir.create(recursive: true);
      }
      
      // Create compressed images directory
      final compressedDir = Directory('${testDir.path}/compressed_images');
      if (!await compressedDir.exists()) {
        await compressedDir.create(recursive: true);
      }
    });

    setUp(() async {
      // Create a test image file (simple 100x100 red square)
      testImageFile = File('${testDir.path}/test_image.jpg');
      
      // Create a simple test image (minimal JPEG header + data)
      // This is a very basic JPEG structure for testing
      final testImageBytes = Uint8List.fromList([
        // JPEG SOI marker
        0xFF, 0xD8,
        // JPEG APP0 marker
        0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00,
        // Add some dummy data to make it larger
        ...List.filled(5000, 0xFF),
        // JPEG EOI marker
        0xFF, 0xD9
      ]);
      
      await testImageFile.writeAsBytes(testImageBytes);
    });

    tearDown(() async {
      // Clean up test files
      if (await testImageFile.exists()) {
        await testImageFile.delete();
      }
      
      // Clean up compressed images
      final compressedDir = Directory('${testDir.path}/compressed_images');
      if (await compressedDir.exists()) {
        await for (final file in compressedDir.list()) {
          if (file is File) {
            await file.delete();
          }
        }
      }
    });

    test('should compress and save image successfully', () async {
      // Arrange
      const fileName = 'test_compressed.jpg';
      
      // Act
      final compressedPath = await ImageCompressionUtil.compressAndSaveImage(
        originalImagePath: testImageFile.path,
        fileName: fileName,
        targetSizeKB: 50,
      );
      
      // Assert
      expect(compressedPath, isNotEmpty);
      expect(compressedPath, contains(fileName));
      
      final compressedFile = File(compressedPath);
      expect(await compressedFile.exists(), isTrue);
      
      // Check file size is reasonable
      final compressedSize = await compressedFile.length();
      expect(compressedSize, greaterThan(0));
      expect(compressedSize, lessThan(100 * 1024)); // Should be less than 100KB
    });

    test('should handle non-existent file gracefully', () async {
      // Arrange
      const nonExistentPath = '/tmp/non_existent_image.jpg';
      const fileName = 'test_compressed.jpg';
      
      // Act & Assert
      expect(
        () => ImageCompressionUtil.compressAndSaveImage(
          originalImagePath: nonExistentPath,
          fileName: fileName,
        ),
        throwsException,
      );
    });

    test('should delete compressed image successfully', () async {
      // Arrange
      const fileName = 'test_to_delete.jpg';
      final compressedPath = await ImageCompressionUtil.compressAndSaveImage(
        originalImagePath: testImageFile.path,
        fileName: fileName,
      );
      
      // Verify file exists
      expect(await File(compressedPath).exists(), isTrue);
      
      // Act
      await ImageCompressionUtil.deleteCompressedImage(compressedPath);
      
      // Assert
      expect(await File(compressedPath).exists(), isFalse);
    });

    test('should handle empty file path in delete gracefully', () async {
      // Act & Assert - should not throw
      await ImageCompressionUtil.deleteCompressedImage('');
    });

    test('should clean up old compressed images', () async {
      // Arrange - create some old files
      final compressedDir = Directory('${testDir.path}/compressed_images');
      final oldFile = File('${compressedDir.path}/old_image.jpg');
      await oldFile.writeAsBytes([1, 2, 3, 4, 5]);
      
      // Modify the file's timestamp to make it appear old
      // Note: This is a simplified test - in real scenarios, you'd need to
      // manipulate file timestamps properly
      
      // Act
      await ImageCompressionUtil.cleanupOldCompressedImages(daysOld: 0);
      
      // Assert - file should be deleted (since we set daysOld to 0)
      expect(await oldFile.exists(), isFalse);
    });
  });
}
